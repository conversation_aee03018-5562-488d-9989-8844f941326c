package com.yuedu.teaching.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuedu.teaching.query.StoreCoursewareDataQuery;
import com.yuedu.teaching.dto.StoreCoursewareDataDTO;
import com.yuedu.teaching.vo.StoreCoursewareDataVO;
import com.yuedu.teaching.entity.StoreCoursewareData;

import java.util.List;

/**
* 门店课件表服务接口
*
* <AUTHOR>
* @date  2025/08/05
*/
public interface StoreCoursewareDataService extends IService<StoreCoursewareData> {



    /**
    * 门店课件表分页查询
    * @param page 分页对象
    * @param storeCoursewareDataQuery 门店课件表
    * @return IPage 分页结果
    */
    IPage page(Page page, StoreCoursewareDataQuery storeCoursewareDataQuery);


    /**
    * 新增门店课件表
    * @param storeCoursewareDataDTO 门店课件表
    * @return boolean 执行结果
    */
    boolean add(StoreCoursewareDataDTO storeCoursewareDataDTO);


    /**
    * 修改门店课件表
    * @param storeCoursewareDataDTO 门店课件表
    * @return boolean 执行结果
    */
    boolean edit(StoreCoursewareDataDTO storeCoursewareDataDTO);


    /**
    * 导出excel 门店课件表表格
    * @param storeCoursewareDataQuery 查询条件
    * @param ids 导出指定ID
    * @return List<StoreCoursewareDataVO> 结果集合
    */
    List<StoreCoursewareDataVO> export(StoreCoursewareDataQuery storeCoursewareDataQuery, Long[] ids);
}
