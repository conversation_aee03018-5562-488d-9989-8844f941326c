package com.yuedu.teaching.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuedu.teaching.query.StoreCoursewareDataStepDetailsQuery;
import com.yuedu.teaching.dto.StoreCoursewareDataStepDetailsDTO;
import com.yuedu.teaching.vo.StoreCoursewareDataStepDetailsVO;
import com.yuedu.teaching.entity.StoreCoursewareDataStepDetails;

import java.util.List;

/**
* 门店教学环节详情表服务接口
*
* <AUTHOR>
* @date  2025/08/05
*/
public interface StoreCoursewareDataStepDetailsService extends IService<StoreCoursewareDataStepDetails> {



    /**
    * 门店教学环节详情表分页查询
    * @param page 分页对象
    * @param storeCoursewareDataStepDetailsQuery 门店教学环节详情表
    * @return IPage 分页结果
    */
    IPage page(Page page, StoreCoursewareDataStepDetailsQuery storeCoursewareDataStepDetailsQuery);


    /**
    * 新增门店教学环节详情表
    * @param storeCoursewareDataStepDetailsDTO 门店教学环节详情表
    * @return boolean 执行结果
    */
    boolean add(StoreCoursewareDataStepDetailsDTO storeCoursewareDataStepDetailsDTO);


    /**
    * 修改门店教学环节详情表
    * @param storeCoursewareDataStepDetailsDTO 门店教学环节详情表
    * @return boolean 执行结果
    */
    boolean edit(StoreCoursewareDataStepDetailsDTO storeCoursewareDataStepDetailsDTO);


    /**
    * 导出excel 门店教学环节详情表表格
    * @param storeCoursewareDataStepDetailsQuery 查询条件
    * @param ids 导出指定ID
    * @return List<StoreCoursewareDataStepDetailsVO> 结果集合
    */
    List<StoreCoursewareDataStepDetailsVO> export(StoreCoursewareDataStepDetailsQuery storeCoursewareDataStepDetailsQuery, Long[] ids);
}
