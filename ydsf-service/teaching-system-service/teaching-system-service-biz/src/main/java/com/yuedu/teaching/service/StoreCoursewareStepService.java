package com.yuedu.teaching.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuedu.teaching.query.StoreCoursewareStepQuery;
import com.yuedu.teaching.dto.StoreCoursewareStepDTO;
import com.yuedu.teaching.vo.StoreCoursewareStepVO;
import com.yuedu.teaching.entity.StoreCoursewareStep;

import java.util.List;

/**
* 门店教学环节表服务接口
*
* <AUTHOR>
* @date  2025/08/05
*/
public interface StoreCoursewareStepService extends IService<StoreCoursewareStep> {



    /**
    * 门店教学环节表分页查询
    * @param page 分页对象
    * @param storeCoursewareStepQuery 门店教学环节表
    * @return IPage 分页结果
    */
    IPage page(Page page, StoreCoursewareStepQuery storeCoursewareStepQuery);


    /**
    * 新增门店教学环节表
    * @param storeCoursewareStepDTO 门店教学环节表
    * @return boolean 执行结果
    */
    boolean add(StoreCoursewareStepDTO storeCoursewareStepDTO);


    /**
    * 修改门店教学环节表
    * @param storeCoursewareStepDTO 门店教学环节表
    * @return boolean 执行结果
    */
    boolean edit(StoreCoursewareStepDTO storeCoursewareStepDTO);


    /**
    * 导出excel 门店教学环节表表格
    * @param storeCoursewareStepQuery 查询条件
    * @param ids 导出指定ID
    * @return List<StoreCoursewareStepVO> 结果集合
    */
    List<StoreCoursewareStepVO> export(StoreCoursewareStepQuery storeCoursewareStepQuery, Long[] ids);
}
