package com.yuedu.teaching.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.beans.BeanUtils;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.teaching.mapper.StoreCoursewareDataStepDetailsMapper;
import com.yuedu.teaching.service.StoreCoursewareDataStepDetailsService;
import com.yuedu.teaching.query.StoreCoursewareDataStepDetailsQuery;
import com.yuedu.teaching.dto.StoreCoursewareDataStepDetailsDTO;
import com.yuedu.teaching.vo.StoreCoursewareDataStepDetailsVO;
import com.yuedu.teaching.entity.StoreCoursewareDataStepDetails;

import java.util.List;


/**
* 门店教学环节详情表服务层
*
* <AUTHOR>
* @date  2025/08/05
*/
@Service
public class StoreCoursewareDataStepDetailsServiceImpl extends ServiceImpl<StoreCoursewareDataStepDetailsMapper,StoreCoursewareDataStepDetails>
    implements StoreCoursewareDataStepDetailsService{


    /**
    * 门店教学环节详情表分页查询
    * @param page 分页对象
    * @param storeCoursewareDataStepDetailsQuery 门店教学环节详情表
    * @return IPage 分页结果
    */
    @Override
    public IPage page(Page page,StoreCoursewareDataStepDetailsQuery storeCoursewareDataStepDetailsQuery) {
        return page(page, Wrappers.<StoreCoursewareDataStepDetails>lambdaQuery());
    }

    /**
    * 新增门店教学环节详情表
    * @param storeCoursewareDataStepDetailsDTO 门店教学环节详情表
    * @return boolean 执行结果
    */
    @Override
    public boolean add(StoreCoursewareDataStepDetailsDTO storeCoursewareDataStepDetailsDTO) {
        StoreCoursewareDataStepDetails storeCoursewareDataStepDetails = new StoreCoursewareDataStepDetails();
        BeanUtils.copyProperties(storeCoursewareDataStepDetailsDTO, storeCoursewareDataStepDetails);
        return save(storeCoursewareDataStepDetails);
    }


    /**
    * 修改门店教学环节详情表
    * @param storeCoursewareDataStepDetailsDTO 门店教学环节详情表
    * @return boolean 执行结果
    */
    @Override
    public boolean edit(StoreCoursewareDataStepDetailsDTO storeCoursewareDataStepDetailsDTO) {
        StoreCoursewareDataStepDetails storeCoursewareDataStepDetails = new StoreCoursewareDataStepDetails();
        BeanUtils.copyProperties(storeCoursewareDataStepDetailsDTO, storeCoursewareDataStepDetails);
        return updateById(storeCoursewareDataStepDetails);
    }



    /**
    * 导出excel 门店教学环节详情表表格
    * @param storeCoursewareDataStepDetailsQuery 查询条件
    * @param ids 导出指定ID
    * @return List<StoreCoursewareDataStepDetailsVO> 结果集合
    */
    @Override
    public List<StoreCoursewareDataStepDetailsVO> export(StoreCoursewareDataStepDetailsQuery storeCoursewareDataStepDetailsQuery, Long[] ids) {
        return list(Wrappers.<StoreCoursewareDataStepDetails>lambdaQuery().in(ArrayUtil.isNotEmpty(ids), StoreCoursewareDataStepDetails::getId, ids))
            .stream()
            .map(entity -> {
                StoreCoursewareDataStepDetailsVO storeCoursewareDataStepDetailsVO = new StoreCoursewareDataStepDetailsVO();
                BeanUtils.copyProperties(entity, storeCoursewareDataStepDetailsVO);
                return storeCoursewareDataStepDetailsVO;
            }).toList();
    }

}
