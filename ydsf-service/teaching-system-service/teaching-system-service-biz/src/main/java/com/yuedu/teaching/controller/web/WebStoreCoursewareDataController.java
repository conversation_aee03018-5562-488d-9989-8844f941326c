package com.yuedu.teaching.controller.web;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.V_A;
import com.yuedu.ydsf.common.core.util.V_E;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.teaching.service.StoreCoursewareDataService;
import com.yuedu.teaching.query.StoreCoursewareDataQuery;
import com.yuedu.teaching.dto.StoreCoursewareDataDTO;
import com.yuedu.teaching.vo.StoreCoursewareDataVO;

import java.io.Serializable;
import java.util.List;

/**
 * 门店课件表控制层
 *
 * <AUTHOR>
 * @date 2025/08/05
 */

@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/storeCoursewareData")
@Tag(description = "store_courseware_data", name = "门店课件表")
@StorePermission
public class WebStoreCoursewareDataController {


    private final StoreCoursewareDataService toreCoursewareDataService;


}
