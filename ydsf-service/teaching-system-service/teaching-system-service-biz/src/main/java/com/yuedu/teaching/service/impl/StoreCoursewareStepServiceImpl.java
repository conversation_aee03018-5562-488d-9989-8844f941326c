package com.yuedu.teaching.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.beans.BeanUtils;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.teaching.mapper.StoreCoursewareStepMapper;
import com.yuedu.teaching.service.StoreCoursewareStepService;
import com.yuedu.teaching.query.StoreCoursewareStepQuery;
import com.yuedu.teaching.dto.StoreCoursewareStepDTO;
import com.yuedu.teaching.vo.StoreCoursewareStepVO;
import com.yuedu.teaching.entity.StoreCoursewareStep;

import java.util.List;


/**
* 门店教学环节表服务层
*
* <AUTHOR>
* @date  2025/08/05
*/
@Service
public class StoreCoursewareStepServiceImpl extends ServiceImpl<StoreCoursewareStepMapper,StoreCoursewareStep>
    implements StoreCoursewareStepService{


    /**
    * 门店教学环节表分页查询
    * @param page 分页对象
    * @param storeCoursewareStepQuery 门店教学环节表
    * @return IPage 分页结果
    */
    @Override
    public IPage page(Page page,StoreCoursewareStepQuery storeCoursewareStepQuery) {
        return page(page, Wrappers.<StoreCoursewareStep>lambdaQuery());
    }

    /**
    * 新增门店教学环节表
    * @param storeCoursewareStepDTO 门店教学环节表
    * @return boolean 执行结果
    */
    @Override
    public boolean add(StoreCoursewareStepDTO storeCoursewareStepDTO) {
        StoreCoursewareStep storeCoursewareStep = new StoreCoursewareStep();
        BeanUtils.copyProperties(storeCoursewareStepDTO, storeCoursewareStep);
        return save(storeCoursewareStep);
    }


    /**
    * 修改门店教学环节表
    * @param storeCoursewareStepDTO 门店教学环节表
    * @return boolean 执行结果
    */
    @Override
    public boolean edit(StoreCoursewareStepDTO storeCoursewareStepDTO) {
        StoreCoursewareStep storeCoursewareStep = new StoreCoursewareStep();
        BeanUtils.copyProperties(storeCoursewareStepDTO, storeCoursewareStep);
        return updateById(storeCoursewareStep);
    }



    /**
    * 导出excel 门店教学环节表表格
    * @param storeCoursewareStepQuery 查询条件
    * @param ids 导出指定ID
    * @return List<StoreCoursewareStepVO> 结果集合
    */
    @Override
    public List<StoreCoursewareStepVO> export(StoreCoursewareStepQuery storeCoursewareStepQuery, Long[] ids) {
        return list(Wrappers.<StoreCoursewareStep>lambdaQuery().in(ArrayUtil.isNotEmpty(ids), StoreCoursewareStep::getId, ids))
            .stream()
            .map(entity -> {
                StoreCoursewareStepVO storeCoursewareStepVO = new StoreCoursewareStepVO();
                BeanUtils.copyProperties(entity, storeCoursewareStepVO);
                return storeCoursewareStepVO;
            }).toList();
    }

}
