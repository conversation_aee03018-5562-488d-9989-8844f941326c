package com.yuedu.teaching.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.beans.BeanUtils;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.teaching.mapper.StoreCoursewareDataMapper;
import com.yuedu.teaching.service.StoreCoursewareDataService;
import com.yuedu.teaching.query.StoreCoursewareDataQuery;
import com.yuedu.teaching.dto.StoreCoursewareDataDTO;
import com.yuedu.teaching.vo.StoreCoursewareDataVO;
import com.yuedu.teaching.entity.StoreCoursewareData;

import java.util.List;


/**
* 门店课件表服务层
*
* <AUTHOR>
* @date  2025/08/05
*/
@Service
public class StoreCoursewareDataServiceImpl extends ServiceImpl<StoreCoursewareDataMapper,StoreCoursewareData>
    implements StoreCoursewareDataService{


    /**
    * 门店课件表分页查询
    * @param page 分页对象
    * @param storeCoursewareDataQuery 门店课件表
    * @return IPage 分页结果
    */
    @Override
    public IPage page(Page page,StoreCoursewareDataQuery storeCoursewareDataQuery) {
        return page(page, Wrappers.<StoreCoursewareData>lambdaQuery());
    }

    /**
    * 新增门店课件表
    * @param storeCoursewareDataDTO 门店课件表
    * @return boolean 执行结果
    */
    @Override
    public boolean add(StoreCoursewareDataDTO storeCoursewareDataDTO) {
        StoreCoursewareData storeCoursewareData = new StoreCoursewareData();
        BeanUtils.copyProperties(storeCoursewareDataDTO, storeCoursewareData);
        return save(storeCoursewareData);
    }


    /**
    * 修改门店课件表
    * @param storeCoursewareDataDTO 门店课件表
    * @return boolean 执行结果
    */
    @Override
    public boolean edit(StoreCoursewareDataDTO storeCoursewareDataDTO) {
        StoreCoursewareData storeCoursewareData = new StoreCoursewareData();
        BeanUtils.copyProperties(storeCoursewareDataDTO, storeCoursewareData);
        return updateById(storeCoursewareData);
    }



    /**
    * 导出excel 门店课件表表格
    * @param storeCoursewareDataQuery 查询条件
    * @param ids 导出指定ID
    * @return List<StoreCoursewareDataVO> 结果集合
    */
    @Override
    public List<StoreCoursewareDataVO> export(StoreCoursewareDataQuery storeCoursewareDataQuery, Long[] ids) {
        return list(Wrappers.<StoreCoursewareData>lambdaQuery().in(ArrayUtil.isNotEmpty(ids), StoreCoursewareData::getId, ids))
            .stream()
            .map(entity -> {
                StoreCoursewareDataVO storeCoursewareDataVO = new StoreCoursewareDataVO();
                BeanUtils.copyProperties(entity, storeCoursewareDataVO);
                return storeCoursewareDataVO;
            }).toList();
    }

}
